import { useAssistant, useDashboard } from '@/api/general';
import { setActionsCount, setUnreadActionsCount } from '@/api/general/actionsStorage';
import { Comment } from '@/api/general/dashboardApi';
import { Ionicons, MaterialIcons } from '@expo/vector-icons';
import { useFocusEffect } from '@react-navigation/native';
import { format } from 'date-fns';
import { fr } from 'date-fns/locale';
import { useRouter } from 'expo-router';
import React, { useCallback, useEffect, useState } from 'react';
import {
    ActivityIndicator,
    FlatList,
    Image,
    Platform,
    RefreshControl,
    SafeAreaView,
    StatusBar,
    StyleSheet,
    Text,
    TouchableOpacity,
    View,
} from 'react-native';

export default function ActionsScreen() {
  const router = useRouter();
  const { userActions, loading, refreshDashboardData, markActionsAsRead } = useDashboard();
  const { assistant } = useAssistant();
  const [refreshing, setRefreshing] = useState(false);
  const [expandedComments, setExpandedComments] = useState<{[key: string]: boolean}>({});
  const [initialLoading, setInitialLoading] = useState(true);

  // Sort actions by update date if available, otherwise by creation date (most recent first)
  const sortedActions = [...userActions].sort((a, b) => {
    const getDate = (item: any) => {
      const action = item.actionDTO || item.actionDto;
      if (action.updateDate) {
        return new Date(action.updateDate).getTime();
      }
      return new Date(action.dateEntry).getTime();
    };

    const dateA = getDate(a);
    const dateB = getDate(b);
    return dateB - dateA;
  });

  useEffect(() => {
    const loadInitialData = async () => {
      try {
        await refreshDashboardData();
      } catch (error) {
        console.error('Error loading initial data:', error);
      } finally {
        setInitialLoading(false);
      }
    };

    loadInitialData();
  }, []);

  // For debugging purposes
  console.log('User actions count:', userActions.length);
  setActionsCount(userActions.length !=0 ? userActions.length : 0);

  

  // Mark actions as read when the screen comes into focus
  useFocusEffect(
    useCallback(() => {
      console.log('Actions screen focused - marking actions as read');
      markActionsAsRead();
      return () => {
        // Cleanup function when screen loses focus (if needed)
      };
    }, [markActionsAsRead])
  );

  // Toggle comments expansion for a specific action
  const toggleComments = (actionId: string) => {
    setExpandedComments(prev => ({
      ...prev,
      [actionId]: !prev[actionId]
    }));
  };

  // Handle pull-to-refresh
  useFocusEffect(
    useCallback(() => {
      onRefresh()
    }, [])
  )
  const onRefresh = async () => {
    try {
      setRefreshing(true);
      await refreshDashboardData();
    } catch (err) {
      console.error('Error refreshing data:', err);
    } finally {
      setRefreshing(false);
    }
  };

  // Format date function
  const formatDate = (dateString: string) => {
    try {
      if (!dateString) return 'N/A';
      const date = new Date(dateString);
      return format(date, 'dd MMM yyyy', { locale: fr });
    } catch (error) {
      console.error('Error formatting date:', error);
      return 'Invalid date';
    }
  };

  // Get status color based on action status
  const getStatusColor = (status: string) => {
    switch (status?.toLowerCase()) {
      case 'en cours':
        return '#FFA500'; // Orange for in progress
      case 'à effectuer':
      case 'terminée':
        return '#4AC29A'; // Green for completed
      case 'annulé':
      case 'annulée':
        return '#FF5252'; // Red for cancelled
      case 'en attente':
        return '#4A7FC2'; // Blue for pending
      default:
        return '#888888'; // Gray for unknown status
    }
  };

  // Render each action item
  const renderActionItem = ({ item }: { item: any }) => {
    // Get the action data from the correct structure
    const actionDTO = item.actionDTO || item.actionDto;
    if (!actionDTO) {
      console.log('Invalid action item:', item);
      return null;
    }

    // Get entity information from the parent item
    const entityInfo = {
      entityId: item.entityId,
      entityType: item.entityType,
      moduleName: item.moduleName,
      modulepath: item.modulepath
    };

    // Log the action structure for debugging

    const statusColor = getStatusColor(actionDTO.actionStatus?.name);

    return (
      <View style={styles.actionCard}>
        <View style={styles.actionHeader}>
          <View style={styles.subjectContainer}>
            <Text style={styles.subjectText} numberOfLines={1}>
              {actionDTO.subject || 'Sans titre'}
            </Text>
          </View>
          <View style={[styles.statusBadge, { backgroundColor: `${statusColor}20` }]}>
            <Text style={[styles.statusText, { color: statusColor }]}>
              {actionDTO.actionStatus?.name || 'Statut inconnu'}
            </Text>
          </View>
        </View>

        <View style={styles.actionDetails}>
          <View style={styles.detailRow}>
            <MaterialIcons name="event" size={16} color="#888" />
            <Text style={styles.detailLabel}>Date d'entrée:</Text>
            <Text style={styles.detailValue}>{formatDate(actionDTO.dateEntry)}</Text>
          </View>

          <View style={styles.detailRow}>
            <MaterialIcons name="timer" size={16} color="#888" />
            <Text style={styles.detailLabel}>Deadline:</Text>
            <Text style={styles.detailValue}>{formatDate(actionDTO.deadline)}</Text>
          </View>

          <View style={styles.detailRow}>
            <MaterialIcons name="folder" size={16} color="#888" />
            <Text style={styles.detailLabel}>Module:</Text>
            <Text style={styles.detailValue}>{entityInfo.moduleName || 'N/A'}</Text>
          </View>

          {entityInfo.entityId && (
            <View style={styles.detailRow}>
              <MaterialIcons name="link" size={16} color="#888" />
              <Text style={styles.detailLabel}>Entité:</Text>
              <Text style={styles.detailValue}>
                {entityInfo.entityType} #{entityInfo.entityId}
              </Text>
            </View>
          )}
          {actionDTO.createdBy && (
            <View style={styles.detailRow}>
              <MaterialIcons name="link" size={16} color="#888" />
              <Text style={styles.detailLabel}>Crée par :</Text>
              <Text style={styles.detailValue}>
                {actionDTO.createdBy.firstName} {actionDTO.createdBy.lastName}
              </Text>
            </View>
          )}
        </View>

        {actionDTO.comments && actionDTO.comments.length > 0 && (
          <TouchableOpacity
            style={styles.commentsSection}
            onPress={() => toggleComments(actionDTO.id.toString())}
            activeOpacity={0.7}
          >
            <View style={styles.commentsSectionHeader}>
              <Text style={styles.commentsSectionTitle}>
                Commentaires ({actionDTO.comments.length})
              </Text>
              <Ionicons
                name={expandedComments[actionDTO.id.toString()] ? "chevron-up" : "chevron-down"}
                size={16}
                color="#666"
              />
            </View>

            {/* Show only the latest non-label comment when collapsed */}
            {!expandedComments[actionDTO.id.toString()] ? (
              // Find the first non-label comment
              (() => {
                const nonLabelComment = actionDTO.comments.find((comment: Comment) => !comment.labelComment);
                if (nonLabelComment) {
                  return (
                    <View style={styles.commentPreview}>
                      <Text>
                        Commentaire récent :
                      </Text>
                      <Text style={styles.commentText} numberOfLines={2}>
                        "{nonLabelComment.content}"
                      </Text>
                    </View>
                  );
                }
                return null;
              })()
            ) : (
              // Show all comments when expanded
              <View style={styles.allCommentsContainer}>
                {actionDTO.comments.map((comment: Comment, index: number) => (
                  <View key={comment.id} style={[
                    styles.commentItem,
                    index < actionDTO.comments.length - 1 && styles.commentItemBorder
                  ]}>
                    <Text style={[
                      styles.commentText,
                      comment.labelComment && styles.systemCommentText
                    ]}>
                      {comment.content}
                    </Text>
                    <View style={styles.commentFooter}>
                      <Text style={styles.commentDate}>
                        {formatDate(comment.date)}
                      </Text>
                    </View>
                  </View>
                ))}
              </View>
            )}
          </TouchableOpacity>
        )}
      </View>
    );
  };

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <Image source={require('@/assets/images/safwa.png')} style={styles.logo} />
        <Text style={styles.headerTitle}>Actions</Text>
        <View style={styles.headerIcons}>
          <TouchableOpacity onPress={async() => {
            router.back()
            await setUnreadActionsCount(0)
            console.log('Unread actions count set to 0');
          }}>
            <Ionicons name="arrow-back" size={24} color="#222" />
          </TouchableOpacity>
        </View>
      </View>

      {/* Main Content */}
      {initialLoading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#4AC29A" />
        </View>
      ) : (
        <>
          {sortedActions.length === 0 ? (
            <View style={styles.emptyContainer}>
              <MaterialIcons name="event-busy" size={64} color="#ccc" />
              <Text style={styles.emptyText}>
                Aucune action trouvée pour vous
              </Text>
            </View>
          ) : (
            <FlatList
              style={Platform.OS === 'android' ? {marginBottom:0} : {marginBottom:30}}
              data={sortedActions}
              renderItem={renderActionItem}
              keyExtractor={(item) => {
                return item.actionDTO?.id?.toString() || item.actionDto?.id?.toString() || Math.random().toString();
              }}
              contentContainerStyle={styles.listContainer}
              refreshControl={
                <RefreshControl
                  refreshing={refreshing}
                  onRefresh={onRefresh}
                  colors={['#4AC29A']}
                />
              }
            />
          )}
        </>
      )}
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F8FAFF',
    paddingTop: Platform.OS === 'android' ? StatusBar.currentHeight : 0,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingBottom: 12,
    backgroundColor: '#F8FAFF',
    borderBottomWidth: 1,
    borderBottomColor: '#F0F0F0',
  },
  logo: {
    width: 43,
    height: 36,
    marginRight: 8,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    flex: 1,
    color: '#222',
  },
  headerIcons: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  emptyText: {
    fontSize: 16,
    color: '#888',
    marginTop: 12,
  },
  listContainer: {
    padding: 16,
  },
  actionCard: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOpacity: 0.05,
    shadowRadius: 6,
    elevation: 2,
  },
  actionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  subjectContainer: {
    flex: 1,
    marginRight: 8,
  },
  subjectText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#222',
  },
  statusBadge: {
    paddingHorizontal: 10,
    paddingVertical: 4,
    borderRadius: 12,
  },
  statusText: {
    fontSize: 12,
    fontWeight: 'bold',
  },
  actionDetails: {
    marginBottom: 12,
  },
  detailRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 6,
  },
  detailLabel: {
    fontSize: 14,
    color: '#888',
    marginLeft: 6,
    marginRight: 4,
  },
  detailValue: {
    fontSize: 14,
    color: '#444',
    flex: 1,
  },
  commentsSection: {
    borderTopWidth: 1,
    borderTopColor: '#f0f0f0',
    paddingTop: 12,
    marginTop: 8,
  },
  commentsSectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 6,
  },
  commentsSectionTitle: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#666',
  },
  commentPreview: {
    backgroundColor: '#f9f9f9',
    padding: 10,
    borderRadius: 8,
    marginTop: 2,
  },
  commentText: {
    fontSize: 13,
    color: '#555',
    marginBottom: 4,
    marginTop: 8,
  },
  systemCommentText: {
    fontStyle: 'italic',
    color: '#888',
  },
  commentAuthor: {
    fontSize: 12,
    color: '#888',
    textAlign: 'right',
  },
  allCommentsContainer: {
    backgroundColor: '#f9f9f9',
    borderRadius: 8,
    padding: 10,
    marginBottom: 8,
    marginTop: 8,
  },
  commentItem: {
    paddingVertical: 8,
  },
  commentItemBorder: {
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
    marginBottom: 8,
  },
  commentFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 4,
  },
  commentDate: {
    fontSize: 11,
    color: '#aaa',
  },
  toggleButton: {
    backgroundColor: '#4AC29A',
    paddingHorizontal: 10,
    paddingVertical: 5,
    borderRadius: 15,
    marginRight: 8,
  },
  toggleButtonText: {
    color: '#FFFFFF',
    fontSize: 12,
    fontWeight: 'bold',
  },
});
