{"name": "assisstant-app", "main": "expo-router/entry", "version": "1.0.0", "scripts": {"start": "expo start", "reset-project": "node ./scripts/reset-project.js", "android": "expo run:android", "ios": "expo run:ios", "web": "expo start --web", "lint": "expo lint"}, "dependencies": {"@azure/msal-browser": "^4.12.0", "@azure/msal-react": "^3.0.12", "@expo/vector-icons": "^14.1.0", "@infinitered/react-native-mlkit-document-scanner": "^3.1.0", "@react-native-async-storage/async-storage": "^2.1.2", "@react-navigation/bottom-tabs": "^7.3.10", "@react-navigation/elements": "^2.3.8", "@react-navigation/native": "^7.1.9", "@react-navigation/stack": "^7.3.2", "date-fns": "^4.1.0", "dotenv": "^16.5.0", "expo": "53.0.9", "expo-auth-session": "~6.1.5", "expo-blur": "~14.1.4", "expo-constants": "~17.1.6", "expo-dev-client": "~5.1.8", "expo-device": "^7.1.4", "expo-document-picker": "~13.1.5", "expo-font": "~13.3.1", "expo-haptics": "~14.1.4", "expo-image": "~2.1.7", "expo-image-picker": "~16.1.4", "expo-linking": "~7.1.4", "expo-location": "^18.1.4", "expo-notifications": "^0.31.2", "expo-random": "^14.0.1", "expo-router": "~5.0.6", "expo-splash-screen": "~0.30.8", "expo-status-bar": "~2.2.3", "expo-symbols": "~0.4.4", "expo-system-ui": "~5.0.7", "expo-web-browser": "~14.1.6", "lucide-react-native": "^0.508.0", "react": "19.0.0", "react-dom": "19.0.0", "react-native": "0.79.2", "react-native-app-auth": "^8.0.2", "react-native-document-scanner-plugin": "^1.0.1", "react-native-gesture-handler": "~2.24.0", "react-native-maps": "^1.20.1", "react-native-msal": "^4.0.4", "react-native-reanimated": "~3.17.4", "react-native-safe-area-context": "^5.4.0", "react-native-screens": "~4.10.0", "react-native-svg": "^14.1.0", "react-native-web": "~0.20.0", "react-native-webview": "^13.13.5"}, "devDependencies": {"@babel/core": "^7.25.2", "@types/react": "~19.0.10", "cross-env": "^7.0.3", "eslint": "^9.25.0", "eslint-config-expo": "~9.2.0", "typescript": "~5.8.3"}, "private": true}