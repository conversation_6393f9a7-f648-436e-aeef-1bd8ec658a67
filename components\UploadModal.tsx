import * as DocumentPicker from 'expo-document-picker';
import * as ImagePicker from 'expo-image-picker';
import { Camera, ChevronRight, FileText, ScanLine, Search, User, X } from 'lucide-react-native';
import React, { useEffect, useRef, useState } from 'react';
import {
  ActivityIndicator,
  Alert,
  Animated,
  Dimensions,
  Image,
  Modal,
  ScrollView,
  StyleSheet,
  Text,
  TextInput,
  TouchableOpacity,
  View,
} from 'react-native';

import { launchDocumentScannerAsync, ResultFormatOptions, ScannerModeOptions } from '@infinitered/react-native-mlkit-document-scanner';

import { getStoredAssistantData } from '@/api/general/assistantApi';
import { Beneficiary, getBeneficiariesByAssistantId } from '@/api/general/beneficiaryApi';





interface UploadModalProps {
  visible: boolean;
  onClose: () => void;
}

type ModalStep = 'beneficiary-selection' | 'file-upload' | 'processing' | 'preview';

export function UploadModal({ visible, onClose }: UploadModalProps) {
  const [selectedFile, setSelectedFile] = useState<any>(null);
  const [currentStep, setCurrentStep] = useState<ModalStep>('beneficiary-selection');
  const [selectedBeneficiary, setSelectedBeneficiary] = useState<Beneficiary | null>(null);
  const [beneficiaries, setBeneficiaries] = useState<Beneficiary[]>([]);
  const [loading, setLoading] = useState(false);
  const [filterText, setFilterText] = useState('');
  const [processedFile, setProcessedFile] = useState<any>(null);
  const [showOriginal, setShowOriginal] = useState(false);
  const scanProgress = useRef(new Animated.Value(0)).current;
  const { width } = Dimensions.get('window');

  // Filter beneficiaries based on search text
  const filteredBeneficiaries = beneficiaries.filter(beneficiary =>
    beneficiary.name.toLowerCase().includes(filterText.toLowerCase()) ||
    beneficiary.independent.toLowerCase().includes(filterText.toLowerCase())
  );

  // Fetch beneficiaries when modal opens
  useEffect(() => {
    if (visible) {
      fetchBeneficiaries();
    }
  }, [visible]);

  // File storage functions
  const saveFileToStorage = async (file: any, beneficiary: Beneficiary) => {
    try {
      const fileData = {
        id: Date.now().toString(),
        name: file.name,
        uri: file.uri,
        type: file.type,
        size: file.size,
        beneficiaryId: beneficiary.id,
        beneficiaryName: beneficiary.name,
        uploadDate: new Date().toISOString(),
        processed: true,
      };

      const existingFiles = await getStoredFiles();
      const updatedFiles = [...existingFiles, fileData];
      await require('@react-native-async-storage/async-storage').default.setItem('uploadedFiles', JSON.stringify(updatedFiles));
      return fileData.id;
    } catch (error) {
      console.error('Error saving file:', error);
      throw error;
    }
  };

  const getStoredFiles = async () => {
    try {
      const files = await require('@react-native-async-storage/async-storage').default.getItem('uploadedFiles');
      return files ? JSON.parse(files) : [];
    } catch (error) {
      console.error('Error getting stored files:', error);
      return [];
    }
  };

  const deleteFileFromStorage = async (fileId: string) => {
    try {
      const existingFiles = await getStoredFiles();
      const updatedFiles = existingFiles.filter((file: any) => file.id !== fileId);
      await require('@react-native-async-storage/async-storage').default.setItem('uploadedFiles', JSON.stringify(updatedFiles));
    } catch (error) {
      console.error('Error deleting file:', error);
    }
  };

  // Scanning animation and processing
  const startScanningAnimation = () => {
    scanProgress.setValue(0);
    Animated.timing(scanProgress, {
      toValue: 1,
      duration: 3000,
      useNativeDriver: false,
    }).start();
  };

  const simulateDocumentProcessing = async (file: any) => {
    // Start scanning animation
    startScanningAnimation();

    // Simulate processing time
    await new Promise(resolve => setTimeout(resolve, 3000));

    // Create processed version (simulate enhanced contrast)
    const processedVersion = {
      ...file,
      processed: true,
      enhancedContrast: true,
    };

    setProcessedFile(processedVersion);
    setCurrentStep('preview');
  };

  const fetchBeneficiaries = async () => {
    try {
      setLoading(true);
      const assistantData = await getStoredAssistantData();
      if (!assistantData || !assistantData.id) {
        Alert.alert('Error', 'Assistant data not found. Please log in again.');
        return;
      }

      const beneficiariesData = await getBeneficiariesByAssistantId(assistantData.id);
      setBeneficiaries(beneficiariesData);
    } catch (error) {
      console.error('Error fetching beneficiaries:', error);
      Alert.alert('Error', 'Failed to load beneficiaries. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleSelectFile = async () => {
    try {
      const result = await DocumentPicker.getDocumentAsync({
        type: '*/*',
        copyToCacheDirectory: true,
      });

      if (!result.canceled && result.assets && result.assets.length > 0) {
        setSelectedFile(result.assets[0]);
      }
    } catch (error) {
      Alert.alert('Error', 'Failed to select file');
    }
  };

  const handleTakePhoto = async () => {
    try {
      // Request camera permissions
      const { status } = await ImagePicker.requestCameraPermissionsAsync();
      if (status !== 'granted') {
        Alert.alert('Permission needed', 'Camera permission is required to take photos');
        return;
      }

      const result = await ImagePicker.launchCameraAsync({
        mediaTypes: 'images',
        allowsEditing: true,
        aspect: [4, 3],
        quality: 1,
      });

      if (!result.canceled && result.assets && result.assets.length > 0) {
        setSelectedFile({
          name: `photo_${Date.now()}.jpg`,
          uri: result.assets[0].uri,
          type: 'image/jpeg',
          size: result.assets[0].fileSize || 0,
        });
      }
    } catch (error) {
      Alert.alert('Error', 'Failed to take photo');
    }
  };

  const handleDocumentScan = async () => {
    try {
      // Launch Google ML Kit Document Scanner
      const result = await launchDocumentScannerAsync({
        pageLimit: 1,
        galleryImportAllowed: false,
        scannerMode: ScannerModeOptions.FULL,
        resultFormats: ResultFormatOptions.ALL,
      });

      // Check if the result contains scanned documents
      if (result && result.pages && result.pages.length > 0) {
        const scannedDocumentUri = result.pages[0]; // pages contains URI strings
        setSelectedFile({
          name: `scanned_document_${Date.now()}.pdf`,
          uri: scannedDocumentUri,
          type: 'application/pdf',
          size: 0, // Size not available from ML Kit
          scanned: true,
          mlkitProcessed: true, // Mark as processed by ML Kit
        });
      } else if (result && result.canceled) {
        // User cancelled the scanning process
        console.log('Document scanning cancelled by user');
      } else {
        Alert.alert('Error', 'Failed to scan document. Please try again.');
      }
    } catch (error) {
      console.error('Document scanning error:', error);
      Alert.alert('Error', 'Failed to scan document. Please try again.');
    }
  };

  const handleSubmit = async () => {
    if (selectedFile && selectedBeneficiary) {
      try {
        setCurrentStep('processing');

        // Save file to storage
        const fileId = await saveFileToStorage(selectedFile, selectedBeneficiary);

        // If it's a document processed by ML Kit, skip simulation and go directly to preview
        if (selectedFile.mlkitProcessed) {
          const processedVersion = {
            ...selectedFile,
            id: fileId,
            processed: true,
            mlkitProcessed: true,
          };
          setProcessedFile(processedVersion);
          setCurrentStep('preview');
        } else if (selectedFile.scanned) {
          // For legacy scanned documents, keep the old behavior
          const processedVersion = {
            ...selectedFile,
            id: fileId,
            processed: true,
            enhancedContrast: true,
            scanned: true,
          };
          setProcessedFile(processedVersion);
          setCurrentStep('preview');
        } else {
          // Start document processing with scanning effect for regular photos/files
          await simulateDocumentProcessing({ ...selectedFile, id: fileId });
        }

      } catch (error) {
        console.error('Error processing file:', error);
        Alert.alert('Error', 'Failed to process document. Please try again.');
        setCurrentStep('file-upload');
      }
    }
  };

  const handleDone = async () => {
    if (processedFile && processedFile.id) {
      // Delete the file from storage when done
      await deleteFileFromStorage(processedFile.id);
    }
    handleClose();
  };

  const handleClose = () => {
    setSelectedFile(null);
    setProcessedFile(null);
    setCurrentStep('beneficiary-selection');
    setSelectedBeneficiary(null);
    setFilterText('');
    setShowOriginal(false);
    scanProgress.setValue(0);
    onClose();
  };

  const handleBeneficiarySelect = (beneficiary: Beneficiary) => {
    setSelectedBeneficiary(beneficiary);
    setCurrentStep('file-upload');
  };

  const handleBackToBeneficiaries = () => {
    setCurrentStep('beneficiary-selection');
    setSelectedFile(null);
  };

  return (
    <Modal
      visible={visible}
      animationType="fade"
      transparent={true}
      onRequestClose={handleClose}
      statusBarTranslucent={true}
    >
      <View style={styles.overlay}>
        <View style={[
          styles.modalContainer,
          {
            width: width * 0.9,
          }
        ]}>
          {/* Header */}
          <View style={styles.header}>
            <Text style={styles.title}>
              {currentStep === 'beneficiary-selection' && 'Select Beneficiary'}
              {currentStep === 'file-upload' && `Upload document for ${selectedBeneficiary?.name}`}
              {currentStep === 'processing' && 'Processing Document...'}
              {currentStep === 'preview' && 'Document Processed'}
            </Text>
            <TouchableOpacity onPress={handleClose} style={styles.closeButton}>
              <X size={24} color="#666" />
            </TouchableOpacity>
          </View>

          {/* Content based on current step */}
          {currentStep === 'beneficiary-selection' ? (
            <>
              {/* Description */}
              <Text style={styles.description}>
                Select a beneficiary to attach the document to:
              </Text>

              {/* Search Filter */}
              <View style={styles.searchContainer}>
                <Search size={20} color="#999" />
                <TextInput
                  style={styles.searchInput}
                  placeholder="Search beneficiaries..."
                  placeholderTextColor="#999"
                  value={filterText}
                  onChangeText={setFilterText}
                  returnKeyType="search"
                  clearButtonMode="while-editing"
                />
              </View>

              {/* Beneficiaries List */}
              {loading ? (
                <View style={styles.loadingContainer}>
                  <ActivityIndicator size="large" color="#00D4AA" />
                  <Text style={styles.loadingText}>Loading beneficiaries...</Text>
                </View>
              ) : (
                <ScrollView
                  style={styles.beneficiariesList}
                  showsVerticalScrollIndicator={false}
                  keyboardShouldPersistTaps="handled"
                  nestedScrollEnabled={true}
                >
                  {filteredBeneficiaries.length > 0 ? (
                    filteredBeneficiaries.map((beneficiary) => (
                      <TouchableOpacity
                        key={beneficiary.id}
                        style={styles.beneficiaryItem}
                        onPress={() => handleBeneficiarySelect(beneficiary)}
                        activeOpacity={0.7}
                      >
                        <View style={styles.beneficiaryInfo}>
                          <View style={styles.beneficiaryAvatar}>
                            <User size={20} color="#999" />
                          </View>
                          <View style={styles.beneficiaryDetails}>
                            <Text style={styles.beneficiaryName}>{beneficiary.name}</Text>
                            <Text style={styles.beneficiaryStatus}>{beneficiary.independent}</Text>
                          </View>
                        </View>
                        <ChevronRight size={20} color="#999" />
                      </TouchableOpacity>
                    ))
                  ) : (
                    <View style={styles.noResultsContainer}>
                      <Text style={styles.noResultsText}>No beneficiaries found</Text>
                      <Text style={styles.noResultsSubtext}>Try adjusting your search terms</Text>
                    </View>
                  )}
                </ScrollView>
              )}
            </>
          ) : currentStep === 'file-upload' ? (
            <>
              {/* Back button */}
              <TouchableOpacity style={styles.backButton} onPress={handleBackToBeneficiaries}>
                <Text style={styles.backButtonText}>← Back to Beneficiaries</Text>
              </TouchableOpacity>

              {/* Description */}
              <Text style={styles.description}>
                Upload a document for {selectedBeneficiary?.name}. This could be an ID card, certificate, or any relevant document.
              </Text>

              {/* File Selection Area */}
              <TouchableOpacity
                style={[
                  styles.fileSelectArea,
                  selectedFile && styles.fileSelectAreaSelected
                ]}
                onPress={handleSelectFile}
              >
                <FileText size={40} color={selectedFile ? "#00D4AA" : "#666"} />
                <Text style={[
                  styles.selectFileText,
                  selectedFile && styles.selectFileTextSelected
                ]}>
                  {selectedFile ? selectedFile.name : 'Select File'}
                </Text>
              </TouchableOpacity>

              {/* Or text */}
              <Text style={styles.orText}>or</Text>

              {/* Camera Button */}
              <TouchableOpacity style={styles.cameraButton} onPress={handleTakePhoto}>
                <Camera size={20} color="#fff" />
                <Text style={styles.cameraButtonText}>Open Camera & Take Photo</Text>
              </TouchableOpacity>

              {/* Or text */}
              <Text style={styles.orText}>or</Text>

              {/* Document Scanner Button */}
              <TouchableOpacity style={styles.scannerButton} onPress={handleDocumentScan}>
                <ScanLine size={20} color="#fff" />
                <Text style={styles.scannerButtonText}>Scan Document</Text>
              </TouchableOpacity>
            </>
          ) : currentStep === 'processing' ? (
            <>
              {/* Processing Animation */}
              <View style={styles.processingContainer}>
                <Text style={styles.processingText}>
                  {selectedFile?.mlkitProcessed ? 'Processing ML Kit scanned document...' :
                   selectedFile?.scanned ? 'Processing scanned document...' : 'Scanning and enhancing document...'}
                </Text>

                {/* Scanning Animation */}
                <View style={styles.scanningContainer}>
                  <View style={styles.documentPreview}>
                    {selectedFile && selectedFile.uri && (
                      <Image source={{ uri: selectedFile.uri }} style={styles.documentImage} />
                    )}
                    {!selectedFile?.scanned && (
                      <Animated.View
                        style={[
                          styles.scanLine,
                          {
                            transform: [{
                              translateY: scanProgress.interpolate({
                                inputRange: [0, 1],
                                outputRange: [0, 200],
                              })
                            }]
                          }
                        ]}
                      />
                    )}
                  </View>
                </View>

                <ActivityIndicator size="large" color="#00D4AA" style={styles.processingSpinner} />
                <Text style={styles.processingSubtext}>
                  {selectedFile?.mlkitProcessed ? 'Finalizing ML Kit processed document...' :
                   selectedFile?.scanned ? 'Finalizing document...' : 'Enhancing contrast and readability...'}
                </Text>
              </View>
            </>
          ) : currentStep === 'preview' ? (
            <>
              {/* Document Preview */}
              <Text style={styles.description}>
                {processedFile?.mlkitProcessed
                  ? 'Document has been scanned and processed by Google ML Kit with automatic enhancement.'
                  : processedFile?.scanned
                  ? 'Document has been scanned and is ready for review.'
                  : 'Document has been processed and enhanced for better readability.'
                }
              </Text>

              {/* Toggle Button */}
              <View style={styles.toggleContainer}>
                <TouchableOpacity
                  style={[styles.toggleButton, !showOriginal && styles.toggleButtonActive]}
                  onPress={() => setShowOriginal(false)}
                >
                  <Text style={[styles.toggleButtonText, !showOriginal && styles.toggleButtonTextActive]}>
                    Enhanced
                  </Text>
                </TouchableOpacity>
                <TouchableOpacity
                  style={[styles.toggleButton, showOriginal && styles.toggleButtonActive]}
                  onPress={() => setShowOriginal(true)}
                >
                  <Text style={[styles.toggleButtonText, showOriginal && styles.toggleButtonTextActive]}>
                    Original
                  </Text>
                </TouchableOpacity>
              </View>

              <View style={styles.previewContainer}>
                {processedFile && processedFile.uri && (
                  <View style={styles.imageContainer}>
                    <Image
                      source={{ uri: processedFile.uri }}
                      style={styles.processedImage}
                    />
                    {!showOriginal && (
                      <>
                        <View style={styles.contrastOverlay} />
                        <View style={styles.blackWhiteOverlay} />
                      </>
                    )}
                  </View>
                )}
                {!showOriginal && (
                  <View style={styles.enhancementBadge}>
                    <Text style={styles.enhancementText}>✓ Enhanced</Text>
                  </View>
                )}
                {showOriginal && (
                  <View style={styles.originalBadge}>
                    <Text style={styles.originalText}>Original</Text>
                  </View>
                )}
              </View>

              <Text style={styles.previewNote}>
                {showOriginal
                  ? 'Original document as uploaded'
                  : processedFile?.mlkitProcessed
                    ? `ML Kit processed document saved for ${selectedBeneficiary?.name}`
                    : processedFile?.scanned
                    ? `Scanned document saved for ${selectedBeneficiary?.name}`
                    : `Enhanced document saved for ${selectedBeneficiary?.name}`
                }
              </Text>
            </>
          ) : null}

          {/* Action Buttons */}
          {currentStep === 'file-upload' && (
            <View style={styles.actionButtons}>
              <TouchableOpacity style={styles.cancelButton} onPress={handleClose}>
                <Text style={styles.cancelButtonText}>Cancel</Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={[
                  styles.submitButton,
                  selectedFile ? styles.submitButtonActive : styles.submitButtonInactive
                ]}
                onPress={handleSubmit}
                disabled={!selectedFile}
              >
                <Text style={[
                  styles.submitButtonText,
                  selectedFile ? styles.submitButtonTextActive : styles.submitButtonTextInactive
                ]}>
                  Submit
                </Text>
              </TouchableOpacity>
            </View>
          )}

          {/* Done Button for Preview */}
          {currentStep === 'preview' && (
            <View style={styles.actionButtons}>
              <TouchableOpacity style={styles.doneButton} onPress={handleDone}>
                <Text style={styles.doneButtonText}>Done</Text>
              </TouchableOpacity>
            </View>
          )}
        </View>
      </View>
    </Modal>
  );
}

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContainer: {
    backgroundColor: '#ffffff',
    borderRadius: 20,
    padding: 24,
    maxHeight: '90%',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 10,
    },
    shadowOpacity: 0.25,
    shadowRadius: 10,
    elevation: 10,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 16,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#1a1a1a',
    flex: 1,
    lineHeight: 30,
  },
  closeButton: {
    padding: 4,
  },
  description: {
    fontSize: 14,
    color: '#666',
    marginBottom: 32,
    lineHeight: 20,
  },
  fileSelectArea: {
    borderWidth: 2,
    borderColor: '#e0e0e0',
    borderStyle: 'dashed',
    borderRadius: 12,
    padding: 40,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 20,
    minHeight: 120,
    backgroundColor: '#f8f9fa',
  },
  fileSelectAreaSelected: {
    borderColor: '#00D4AA',
    backgroundColor: 'rgba(0, 212, 170, 0.1)',
  },
  selectFileText: {
    marginTop: 12,
    fontSize: 16,
    color: '#666',
  },
  selectFileTextSelected: {
    color: '#00D4AA',
  },
  orText: {
    textAlign: 'center',
    color: '#666',
    fontSize: 16,
    marginBottom: 20,
  },
  cameraButton: {
    backgroundColor: '#007AFF',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 16,
    borderRadius: 12,
    marginBottom: 32,
  },
  cameraButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 8,
  },
  scannerButton: {
    backgroundColor: '#00D4AA',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 16,
    borderRadius: 12,
    marginBottom: 32,
  },
  scannerButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 8,
  },
  actionButtons: {
    flexDirection: 'row',
    gap: 12,
  },
  cancelButton: {
    flex: 1,
    backgroundColor: '#f0f0f0',
    padding: 16,
    borderRadius: 12,
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#e0e0e0',
  },
  cancelButtonText: {
    color: '#666',
    fontSize: 16,
    fontWeight: '600',
  },
  submitButton: {
    flex: 1,
    padding: 16,
    borderRadius: 12,
    alignItems: 'center',
  },
  submitButtonActive: {
    backgroundColor: '#00D4AA',
  },
  submitButtonInactive: {
    backgroundColor: '#e0e0e0',
  },
  submitButtonText: {
    fontSize: 16,
    fontWeight: '600',
  },
  submitButtonTextActive: {
    color: '#fff',
  },
  submitButtonTextInactive: {
    color: '#999',
  },
  // New styles for beneficiary selection
  loadingContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 40,
  },
  loadingText: {
    color: '#666',
    marginTop: 12,
    fontSize: 16,
  },
  beneficiariesList: {
    maxHeight: 300,
    marginBottom: 20,
  },
  beneficiaryItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 16,
    backgroundColor: '#f8f9fa',
    borderRadius: 12,
    marginBottom: 12,
    borderWidth: 1,
    borderColor: '#e0e0e0',
  },
  beneficiaryInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  beneficiaryAvatar: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#e0e0e0',
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },
  beneficiaryDetails: {
    flex: 1,
  },
  beneficiaryName: {
    color: '#1a1a1a',
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
  },
  beneficiaryStatus: {
    color: '#666',
    fontSize: 14,
  },
  backButton: {
    alignSelf: 'flex-start',
    marginBottom: 16,
  },
  backButtonText: {
    color: '#00D4AA',
    fontSize: 16,
    fontWeight: '600',
  },
  // Search filter styles
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f8f9fa',
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 12,
    marginBottom: 20,
    borderWidth: 1,
    borderColor: '#e0e0e0',
  },
  searchInput: {
    flex: 1,
    marginLeft: 12,
    fontSize: 16,
    color: '#1a1a1a',
  },
  // No results styles
  noResultsContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 40,
  },
  noResultsText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#666',
    marginBottom: 8,
  },
  noResultsSubtext: {
    fontSize: 14,
    color: '#999',
  },
  // Processing styles
  processingContainer: {
    alignItems: 'center',
    paddingVertical: 40,
  },
  processingText: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1a1a1a',
    marginBottom: 30,
    textAlign: 'center',
  },
  processingSubtext: {
    fontSize: 14,
    color: '#666',
    marginTop: 20,
    textAlign: 'center',
  },
  processingSpinner: {
    marginTop: 20,
  },
  // Scanning animation styles
  scanningContainer: {
    width: '100%',
    alignItems: 'center',
    marginVertical: 20,
  },
  documentPreview: {
    width: 200,
    height: 200,
    borderRadius: 12,
    overflow: 'hidden',
    position: 'relative',
    backgroundColor: '#f0f0f0',
    borderWidth: 2,
    borderColor: '#e0e0e0',
  },
  documentImage: {
    width: '100%',
    height: '100%',
    resizeMode: 'cover',
  },
  scanLine: {
    position: 'absolute',
    left: 0,
    right: 0,
    height: 3,
    backgroundColor: '#00D4AA',
    shadowColor: '#00D4AA',
    shadowOffset: { width: 0, height: 0 },
    shadowOpacity: 0.8,
    shadowRadius: 4,
    elevation: 4,
  },
  // Preview styles
  previewContainer: {
    alignItems: 'center',
    marginVertical: 20,
    position: 'relative',
  },
  processedImage: {
    width: 250,
    height: 300,
    borderRadius: 12,
    resizeMode: 'cover',
    backgroundColor: '#ffffff',
    borderWidth: 2,
    borderColor: '#e0e0e0',
    // Enhanced contrast effect
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  enhancementBadge: {
    position: 'absolute',
    top: 10,
    right: 10,
    backgroundColor: '#00D4AA',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 20,
  },
  enhancementText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: '600',
  },
  previewNote: {
    fontSize: 14,
    color: '#666',
    textAlign: 'center',
    marginTop: 10,
  },
  // Done button styles
  doneButton: {
    backgroundColor: '#00D4AA',
    paddingVertical: 16,
    paddingHorizontal: 32,
    borderRadius: 12,
    flex: 1,
    alignItems: 'center',
  },
  doneButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
  // Toggle button styles
  toggleContainer: {
    flexDirection: 'row',
    backgroundColor: '#f0f0f0',
    borderRadius: 12,
    padding: 4,
    marginBottom: 20,
    alignSelf: 'center',
  },
  toggleButton: {
    paddingVertical: 8,
    paddingHorizontal: 20,
    borderRadius: 8,
    backgroundColor: 'transparent',
  },
  toggleButtonActive: {
    backgroundColor: '#00D4AA',
  },
  toggleButtonText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#666',
  },
  toggleButtonTextActive: {
    color: '#fff',
  },
  // Image filter styles
  imageContainer: {
    position: 'relative',
    width: 250,
    height: 300,
    borderRadius: 12,
    overflow: 'hidden',
  },
  contrastOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.2)',
    opacity: 0.8,
  },
  blackWhiteOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(255, 255, 255, 0.4)',
    opacity: 0.6,
  },
  originalImage: {
    // No filter - original image
  },
  enhancedImage: {
    // Black and white filter effect
    tintColor: undefined, // This will be handled by a filter library if needed
  },
  originalBadge: {
    position: 'absolute',
    top: 10,
    right: 10,
    backgroundColor: '#666',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 20,
  },
  originalText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: '600',
  },
});
